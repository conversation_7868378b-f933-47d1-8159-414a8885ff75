//
// Simple Fibonacci Per Bar Strategy
// Converted from Pine Script
//
#region Using declarations
using System;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public class FibonacciPerBarStrategy : Strategy
    {
        #region Variables
        // Settings
        private bool useMtfMode = false;
        private int mtfPeriodValue = 5;
        private bool showSignals = true;
        
        // Fibonacci levels
        private double greenLevel = double.NaN;  // 1.0 level (100%)
        private double redLevel = double.NaN;    // 0.0 level (0%)
        private double tpBuyLevel = double.NaN;  // 1.1 level (110%)
        private double tpSellLevel = double.NaN; // -0.1 level (-10%)
        
        // Position tracking
        private bool inLongPosition = false;
        private bool inShortPosition = false;
        #endregion

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Simple Fibonacci Per Bar Strategy";
                Name = "FibonacciPerBarStrategy";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;
                IsInstantiatedOnEachOptimizationIteration = false;
            }
            else if (State == State.Configure)
            {
                // Add MTF data series if enabled
                if (useMtfMode)
                {
                    AddDataSeries(BarsPeriodType.Minute, mtfPeriodValue);
                }
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < BarsRequiredToTrade)
                return;

            // Only process on primary data series
            if (BarsInProgress != 0)
                return;

            // Calculate Fibonacci levels based on current bar or MTF
            CalculateFibLevels();

            // Check for trading signals
            CheckForSignals();
        }

        private void CalculateFibLevels()
        {
            double high, low;

            if (useMtfMode && BarsArray.Length > 1)
            {
                // Use MTF data
                var mtfBars = BarsArray[1];
                if (mtfBars.Count < 1) return;
                
                high = mtfBars.GetHigh(mtfBars.Count - 1);
                low = mtfBars.GetLow(mtfBars.Count - 1);
            }
            else
            {
                // Use current bar data
                high = High[0];
                low = Low[0];
            }

            double range = high - low;
            if (range > 0)
            {
                greenLevel = low + (range * 1.0);    // 100% level (green line)
                redLevel = low + (range * 0.0);      // 0% level (red line)
                tpBuyLevel = low + (range * 1.1);    // 110% level (buy TP)
                tpSellLevel = low + (range * -0.1);  // -10% level (sell TP)

                // Draw levels on chart
                if (showSignals)
                {
                    Draw.Line(this, "GreenLevel" + CurrentBar, 0, greenLevel, 1, greenLevel, Brushes.Green);
                    Draw.Line(this, "RedLevel" + CurrentBar, 0, redLevel, 1, redLevel, Brushes.Red);
                }
            }
        }

        private void CheckForSignals()
        {
            if (!showSignals || double.IsNaN(greenLevel) || double.IsNaN(redLevel))
                return;

            // Check for buy signal (price hits green level)
            if (High[0] >= greenLevel && Low[0] <= greenLevel && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLong(1, "FibBuy");
                inLongPosition = true;
                
                // Set TP and SL
                SetProfitTarget("FibBuy", CalculationMode.Price, tpBuyLevel);
                SetStopLoss("FibBuy", CalculationMode.Price, redLevel, false);
                
                Draw.ArrowUp(this, "BuySignal" + CurrentBar, false, 0, Low[0] - TickSize, Brushes.Green);
                Print("BUY signal at " + greenLevel.ToString("F2"));
            }

            // Check for sell signal (price hits red level)
            if (Low[0] <= redLevel && High[0] >= redLevel && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShort(1, "FibSell");
                inShortPosition = true;
                
                // Set TP and SL
                SetProfitTarget("FibSell", CalculationMode.Price, tpSellLevel);
                SetStopLoss("FibSell", CalculationMode.Price, greenLevel, false);
                
                Draw.ArrowDown(this, "SellSignal" + CurrentBar, false, 0, High[0] + TickSize, Brushes.Red);
                Print("SELL signal at " + redLevel.ToString("F2"));
            }
        }

        protected override void OnPositionUpdate(Position position, double averagePrice, int quantity, MarketPosition marketPosition)
        {
            if (marketPosition == MarketPosition.Flat)
            {
                inLongPosition = false;
                inShortPosition = false;
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Display(Name = "Use Multi-Timeframe Mode", Description = "Enable MTF mode", Order = 1, GroupName = "Settings")]
        public bool UseMtfMode
        {
            get { return useMtfMode; }
            set { useMtfMode = value; }
        }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "MTF Period Value", Description = "MTF Period in minutes", Order = 2, GroupName = "Settings")]
        public int MtfPeriodValue
        {
            get { return mtfPeriodValue; }
            set { mtfPeriodValue = value; }
        }

        [NinjaScriptProperty]
        [Display(Name = "Show Signals", Description = "Show buy/sell signals", Order = 3, GroupName = "Settings")]
        public bool ShowSignals
        {
            get { return showSignals; }
            set { showSignals = value; }
        }
        #endregion

    }
}
